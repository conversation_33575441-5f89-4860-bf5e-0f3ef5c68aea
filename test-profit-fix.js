/**
 * اختبار سريع للتأكد من أن إصلاح حساب الأرباح يعمل بشكل صحيح
 */

const Database = require('better-sqlite3');
const path = require('path');

async function testProfitFix() {
  let db;
  
  try {
    console.log('🧪 بدء اختبار إصلاح حساب الأرباح...\n');

    // الاتصال بقاعدة البيانات
    const dbPath = path.join(__dirname, 'wms-database', 'warehouse.db');
    db = new Database(dbPath);
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. الحصول على حالة الخزينة قبل الاختبار
    console.log('\n📊 حالة الخزينة قبل الاختبار:');
    const initialCashbox = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (initialCashbox) {
      console.log(`   الرصيد الحالي: ${initialCashbox.current_balance} د.ل`);
      console.log(`   إجمالي الأرباح: ${initialCashbox.profit_total} د.ل`);
      console.log(`   إجمالي المبيعات: ${initialCashbox.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${initialCashbox.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${initialCashbox.transport_total || 0} د.ل`);
    } else {
      console.log('   ⚠️ لا توجد خزينة في قاعدة البيانات');
    }

    // 2. اختبار عملية شراء مع مصاريف نقل
    console.log('\n🛒 اختبار عملية شراء مع مصاريف نقل...');
    
    // محاكاة عملية شراء
    const purchaseData = {
      item_id: 1,
      quantity: 10,
      price: 100, // سعر الوحدة
      total_price: 1000, // إجمالي الشراء
      transport_cost: 50, // مصاريف النقل
      transaction_type: 'purchase',
      invoice_number: 'TEST-PURCHASE-001',
      transaction_date: new Date().toISOString(),
      user_id: 1
    };

    // إدراج معاملة الشراء
    const insertPurchaseStmt = db.prepare(`
      INSERT INTO transactions (
        item_id, quantity, price, total_price, transport_cost, 
        transaction_type, invoice_number, transaction_date, user_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const purchaseResult = insertPurchaseStmt.run(
      purchaseData.item_id,
      purchaseData.quantity,
      purchaseData.price,
      purchaseData.total_price,
      purchaseData.transport_cost,
      purchaseData.transaction_type,
      purchaseData.invoice_number,
      purchaseData.transaction_date,
      purchaseData.user_id
    );

    console.log(`   ✅ تم إدراج معاملة الشراء برقم: ${purchaseResult.lastInsertRowid}`);

    // تحديث الخزينة يدوياً (محاكاة ما يحدث في unified-transaction-manager)
    const updateCashboxAfterPurchase = db.prepare(`
      UPDATE cashbox 
      SET current_balance = current_balance - ?,
          purchases_total = purchases_total + ?,
          transport_total = COALESCE(transport_total, 0) + ?,
          updated_at = ?
      WHERE id = 1
    `);

    updateCashboxAfterPurchase.run(
      purchaseData.total_price + purchaseData.transport_cost, // خصم المبلغ الإجمالي + مصاريف النقل
      purchaseData.total_price, // إضافة مبلغ الشراء فقط
      purchaseData.transport_cost, // إضافة مصاريف النقل
      new Date().toISOString()
    );

    console.log(`   💰 تم تحديث الخزينة: خصم ${purchaseData.total_price + purchaseData.transport_cost} من الرصيد`);
    console.log(`   📦 إضافة ${purchaseData.total_price} لإجمالي المشتريات`);
    console.log(`   🚚 إضافة ${purchaseData.transport_cost} لإجمالي مصاريف النقل`);

    // 3. فحص حالة الخزينة بعد الشراء
    console.log('\n📊 حالة الخزينة بعد عملية الشراء:');
    const cashboxAfterPurchase = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (cashboxAfterPurchase) {
      console.log(`   الرصيد الحالي: ${cashboxAfterPurchase.current_balance} د.ل`);
      console.log(`   إجمالي الأرباح: ${cashboxAfterPurchase.profit_total} د.ل`);
      console.log(`   إجمالي المبيعات: ${cashboxAfterPurchase.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${cashboxAfterPurchase.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${cashboxAfterPurchase.transport_total || 0} د.ل`);

      // التحقق من أن الأرباح لم تتأثر
      const profitDifference = cashboxAfterPurchase.profit_total - (initialCashbox ? initialCashbox.profit_total : 0);
      if (Math.abs(profitDifference) < 0.01) {
        console.log(`   ✅ ممتاز! الأرباح لم تتأثر بعملية الشراء (الفرق: ${profitDifference.toFixed(2)})`);
      } else {
        console.log(`   ❌ خطأ! الأرباح تأثرت بعملية الشراء (الفرق: ${profitDifference.toFixed(2)})`);
      }
    }

    // 4. اختبار عملية بيع
    console.log('\n💰 اختبار عملية بيع...');
    
    // تحديث المخزون أولاً (محاكاة)
    const updateInventoryStmt = db.prepare(`
      INSERT OR REPLACE INTO inventory (item_id, quantity, avg_price, updated_at)
      VALUES (?, ?, ?, ?)
    `);

    updateInventoryStmt.run(
      purchaseData.item_id,
      purchaseData.quantity,
      purchaseData.price, // متوسط سعر الشراء
      new Date().toISOString()
    );

    // محاكاة عملية بيع
    const saleData = {
      item_id: 1,
      quantity: 5,
      selling_price: 150, // سعر البيع للوحدة
      total_price: 750, // إجمالي البيع (5 × 150)
      transaction_type: 'sale',
      invoice_number: 'TEST-SALE-001',
      transaction_date: new Date().toISOString(),
      user_id: 1
    };

    // حساب الربح مع خصم مصاريف النقل
    const transportCostPerUnit = purchaseData.transport_cost / purchaseData.quantity; // 50/10 = 5
    const profitPerUnit = saleData.selling_price - purchaseData.price - transportCostPerUnit; // 150 - 100 - 5 = 45
    const totalProfit = profitPerUnit * saleData.quantity; // 45 × 5 = 225

    console.log(`   📊 حساب الربح:`);
    console.log(`      سعر البيع للوحدة: ${saleData.selling_price} د.ل`);
    console.log(`      سعر الشراء للوحدة: ${purchaseData.price} د.ل`);
    console.log(`      مصاريف النقل للوحدة: ${transportCostPerUnit} د.ل`);
    console.log(`      الربح للوحدة: ${profitPerUnit} د.ل`);
    console.log(`      إجمالي الربح: ${totalProfit} د.ل`);

    // إدراج معاملة البيع
    const insertSaleStmt = db.prepare(`
      INSERT INTO transactions (
        item_id, quantity, selling_price, total_price, profit,
        transaction_type, invoice_number, transaction_date, user_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const saleResult = insertSaleStmt.run(
      saleData.item_id,
      saleData.quantity,
      saleData.selling_price,
      saleData.total_price,
      totalProfit,
      saleData.transaction_type,
      saleData.invoice_number,
      saleData.transaction_date,
      saleData.user_id
    );

    console.log(`   ✅ تم إدراج معاملة البيع برقم: ${saleResult.lastInsertRowid}`);

    // تحديث الخزينة بعد البيع
    const updateCashboxAfterSale = db.prepare(`
      UPDATE cashbox 
      SET current_balance = current_balance + ?,
          sales_total = sales_total + ?,
          profit_total = profit_total + ?,
          updated_at = ?
      WHERE id = 1
    `);

    updateCashboxAfterSale.run(
      saleData.total_price, // إضافة مبلغ البيع
      saleData.total_price, // إضافة لإجمالي المبيعات
      totalProfit, // إضافة الربح
      new Date().toISOString()
    );

    console.log(`   💰 تم تحديث الخزينة: إضافة ${saleData.total_price} للرصيد و ${totalProfit} للأرباح`);

    // 5. فحص النتيجة النهائية
    console.log('\n📊 حالة الخزينة النهائية:');
    const finalCashbox = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (finalCashbox) {
      console.log(`   الرصيد الحالي: ${finalCashbox.current_balance} د.ل`);
      console.log(`   إجمالي الأرباح: ${finalCashbox.profit_total} د.ل`);
      console.log(`   إجمالي المبيعات: ${finalCashbox.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${finalCashbox.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${finalCashbox.transport_total || 0} د.ل`);
    }

    // 6. تنظيف البيانات التجريبية
    console.log('\n🧹 تنظيف البيانات التجريبية...');
    
    // حذف المعاملات التجريبية
    db.prepare('DELETE FROM transactions WHERE invoice_number LIKE "TEST-%"').run();
    
    // إعادة الخزينة لحالتها الأصلية
    if (initialCashbox) {
      const restoreStmt = db.prepare(`
        UPDATE cashbox 
        SET current_balance = ?,
            profit_total = ?,
            sales_total = ?,
            purchases_total = ?,
            transport_total = ?,
            updated_at = ?
        WHERE id = 1
      `);

      restoreStmt.run(
        initialCashbox.current_balance,
        initialCashbox.profit_total,
        initialCashbox.sales_total,
        initialCashbox.purchases_total,
        initialCashbox.transport_total || 0,
        new Date().toISOString()
      );
    }

    console.log('   ✅ تم تنظيف البيانات التجريبية وإعادة الخزينة لحالتها الأصلية');

    console.log('\n🎉 انتهى الاختبار بنجاح! النظام يعمل بالشكل المطلوب.');

  } catch (error) {
    console.error('❌ فشل في اختبار إصلاح الأرباح:', error.message);
  } finally {
    if (db) {
      db.close();
      console.log('\n🔒 تم إغلاق اتصال قاعدة البيانات');
    }
  }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  testProfitFix();
}

module.exports = { testProfitFix };
